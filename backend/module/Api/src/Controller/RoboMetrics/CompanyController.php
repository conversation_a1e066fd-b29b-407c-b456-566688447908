<?php

declare(strict_types=1);

namespace Api\Controller\RoboMetrics;

class CompanyController extends BaseController
{
    /**
     *
     * @return array
     */
    public function getCompaniesAction(): array
    {
        return [
            'result' => $this->roboMetrics()->company()->getCompaniesForRoboMetrics(),
        ];
    }

    /**
     * @return array
     */
    public function getCompaniesCallsVolumeAction(): array
    {
        $request = (new \STRoboMetrics\Request\Company\CompaniesCallsVolumeRequest())
            ->setStartDate(\Carbon\Carbon::parse($this->getApiParam('start_date'))->startOfDay())
            ->setEndDate(\Carbon\Carbon::parse($this->getApiParam('end_date'))->endOfDay());

        return [
            'result' => $this->roboMetrics()->company()->getCompaniesCallsVolume($request),
        ];
    }
}
