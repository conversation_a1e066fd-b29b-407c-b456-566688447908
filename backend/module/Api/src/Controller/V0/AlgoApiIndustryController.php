<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STAlgo\Validator\AlgoApiExistsValidator;
use STAlgo\Validator\Industry\ConnectIndustryValidator;
use STAlgo\Validator\Industry\DisconnectIndustryValidator;
use STApi\Entity\Exception\NotFoundApiException;
use STApi\Entity\Exception\ValidationApiException;

final class AlgoApiIndustryController extends BaseController
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function getIndustriesAction(): array
    {
        $algoApiId = (int) $this->getApiParam('algo_api_id');

        /** @var AlgoApiExistsValidator $validator */
        $validator = $this->getServiceManager()->get(AlgoApiExistsValidator::class);
        $validator->setInstance($algoApiId);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        return [
            'industries' => $this->algo()->industrySelector()->getIndustries($algoApiId)->toArray(),
        ];
    }

    /**
     * @return array
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     * @throws NotFoundApiException
     */
    public function connectIndustryAction(): array
    {
        $algoApiIdId = (int) $this->getApiParam('algo_api_id');
        $industryId = (int) $this->getApiParam('industry_id');

        /** @var ConnectIndustryValidator $validator */
        $validator = $this->getServiceManager()->get(ConnectIndustryValidator::class);
        $validator->setInstance(['industry_id' => $industryId, 'algo_api_id' => $algoApiIdId]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $industry = $this->algo()->industryConnector()->connect($industryId, $algoApiIdId);

        return [
            'industry' => $industry->toArray(),
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ValidationApiException
     */
    public function disconnectIndustryAction(): array
    {
        $algoApiIdId = (int) $this->getApiParam('algo_api_id');
        $industryId = (int) $this->getApiParam('industry_id');

        /** @var DisconnectIndustryValidator $validator */
        $validator = $this->getServiceManager()->get(DisconnectIndustryValidator::class);
        $validator->setInstance(['industry_id' => $industryId, 'algo_api_id' => $algoApiIdId]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $this->algo()->industryConnector()->disconnect($industryId, $algoApiIdId);

        return [
            'is_deleted' => true,
            'message' => 'Successfully disconnected industry from algo api.',
        ];
    }
}
