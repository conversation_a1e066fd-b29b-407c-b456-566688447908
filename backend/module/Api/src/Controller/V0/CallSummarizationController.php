<?php

declare(strict_types=1);

namespace Api\Controller\V0;

use Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use ReflectionException;
use STApi\Entity\Exception\ValidationApiException;
use STCall\Validator\CallExistsValidator;

final class CallSummarizationController extends BaseController
{
    /**
     * @return null[]
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ReflectionException
     * @throws Exception
     */
    public function getCallSummarizationAction(): array
    {
        $callId = $this->getApiParam('call_id');

        $user = $this->company()->user()->getUser($this->company->getId(), $this->auth()->getUser()->getId());

        /** @var CallExistsValidator $validator */
        $validator = $this->getServiceManager()->get(CallExistsValidator::class);
        $validator->setInstance([
            'call_id' => $callId,
            'company_id' => $this->company->getId(),
            'role_id' => $user->getRole()->getId()
        ]);
        $validator->validate();
        if ($validator->hasError()) {
            throw new ValidationApiException(json_encode($validator->getAllErrorsByGroups()));
        }

        $summarizationData = $this->call()->callSummarizationSelector()->getCallSummarization(
            $callId,
            $this->company->getId()
        );

        return [
            'summary' => $summarizationData ?: null,
        ];
    }
}
