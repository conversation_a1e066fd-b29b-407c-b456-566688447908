<?php

declare(strict_types=1);

namespace STAlgo\Service\ParamsBuilding;

use ReflectionException;
use ST<PERSON>all\Entity\Call;
use STCall\Helper\LanguageHelper;
use STCall\Service\CallService;
use STCompany\Entity\Company;

class RequestParamsBuilder
{
    public function __construct(private readonly CallService $callService)
    {
    }

    /**
     * @throws ReflectionException
     */
    public function build(Company $company, Call $call): RequestParams
    {
        $paragraphs = $this->callService->getParagraphs($company, $call->getId());

        $params = [
            'call_id' => $call->getId(),
            'company_id' => $company->getId(),
        ];
        if (LanguageHelper::hasLanguageCodeByLanguage($call->getLanguage())) {
            $params['language'] = LanguageHelper::getLanguageCodeByLanguage($call->getLanguage());
        }

        foreach ($paragraphs as $paragraph) {
            $params['paragraphs'][] = [
                'id' => $paragraph->getParagraphNumber(),
                'text' => $paragraph->getText(),
                'en_text' => $paragraph->getEnText() ?? $paragraph->getText(),
                'speaker_number' => $paragraph->getSpeakerNumber(),
            ];
        }

        return new RequestParams($params);
    }
}
