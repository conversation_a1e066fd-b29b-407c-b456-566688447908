<?php

declare(strict_types=1);

namespace STCompany\Data;

use Exception;
use Lam<PERSON>\Db\ResultSet\ResultSet;
use Laminas\Db\Sql\Where;
use STApi\Entity\Exception\NotFoundApiException;
use ST<PERSON>all\Entity\Import\Result\User;
use STLib\Db\AbstractTable;

class UsersCompaniesRolesTable extends AbstractTable
{
    /**
     *
     * @param int $companyId
     * @param User $user
     * @return ResultSet
     */
    public function getUserId(int $companyId, User $user): ResultSet
    {
        return $this->tableGateway->select([
            'company_id' => $companyId,
            'company_user_id' => $user->getId(),
        ]);
    }

    /**
     * @param $companyId
     * @param $roleId
     * @return ResultSet
     */
    public function getUser($companyId, $roleId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->join(
            [
                'users' => 'users',
            ],
            'users.user_id = users_companies_roles.user_id',
            [
                'user_name',
                'user_email',
                'user_avatar',
            ],
            \Laminas\Db\Sql\Select::JOIN_INNER
        )
        ->where([
            'users_companies_roles.company_id' => $companyId,
            'users_companies_roles.role_id' => $roleId
        ]);

        $result = $this->tableGateway->selectWith($select);
        return $result;
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @return ResultSet
     * @throws NotFoundApiException
     */
    public function getActivity(int $companyId, int $userId): ResultSet
    {
        $select = $this->tableGateway->getSql()->select();
        $select->columns([
            'is_active',
        ]);
        $select->where([
            'company_id' => $companyId,
            'user_id' => $userId,
        ]);
        $result = $this->tableGateway->selectWith($select);
        if ($result->count() === 0) {
            throw new NotFoundApiException('Company user not found');
        }

        return $result;
    }

    /**
     *
     * @param int $userId
     * @param int $companyId
     * @param int|null $roleId
     * @param string|int $companyUserId
     * @param bool $isAutoAnalyzeCalls
     * @return int
     */
    public function saveUserData(int $userId, int $companyId, ?int $roleId, string|int $companyUserId = null, bool $isAutoAnalyzeCalls = true): int
    {
        return $this->saveUsersData(
            [
                'user_id' => $userId,
            ],
            $companyId,
            $roleId,
            $companyUserId,
            $isAutoAnalyzeCalls
        );
    }

    /**
     *
     * @param array $userIds
     * @param int $companyId
     * @param int|null $roleId
     * @param string|int|null $companyUserId
     * @param bool $isAutoAnalyzeCalls
     * @return int
     * @throws Exception
     */
    public function saveUsersData(array $userIds, int $companyId, ?int $roleId, string|int $companyUserId = null, bool $isAutoAnalyzeCalls = true): int
    {
        $data = [];
        foreach ($userIds as $userId) {
            $data[] = [
                'user_id' => $userId,
                'company_id' => $companyId,
                'role_id' => $roleId,
                'company_user_id' => $companyUserId,
                'is_auto_analyze_calls' => $isAutoAnalyzeCalls,
            ];
        }
        return $this->multiInsertOrUpdate($data, [
            'role_id',
            'company_user_id',
            'is_auto_analyze_calls',
        ], useTransaction: false);
    }

    /**
     *
     * @param int $companyId
     * @param array $userIds
     * @param bool $autoAnalyzeCalls
     * @return int
     */
    public function setAutoAnalyzeForCompanyUsers(int $companyId, array $userIds, bool $autoAnalyzeCalls): int
    {
        $where = [
            'company_id' => $companyId,
        ];

        if (!empty($userIds)) {
            $where['user_id'] = $userIds;
        }

        return $this->tableGateway->update([
            'is_auto_analyze_calls' => $autoAnalyzeCalls,
        ], $where);
    }

    /**
     *
     * @param int $companyId
     * @param int $userId
     * @param bool $isActive
     * @return int
     */
    public function changeActivity(int $companyId, int $userId, bool $isActive): int
    {
        return $this->tableGateway->update([
            'is_active' => $isActive,
        ], [
            'company_id' => $companyId,
            'user_id' => $userId
        ]);
    }

    /**
     *
     * @param int $userId
     * @param int $companyId
     * @return int
     */
    public function deleteUserRole(int $userId, int $companyId): int
    {
        return $this->tableGateway->delete([
            'user_id' => $userId,
            'company_id' => $companyId
        ]);
    }

    public function isUserAlreadyExists(?int $userId, int $excludeCompanyId): bool
    {
        $select = $this->tableGateway->getSql()->select();
        $select
            ->where(function (Where $where) use ($userId, $excludeCompanyId) {
                $where->equalTo('user_id', $userId);
                $where->notEqualTo('company_id', $excludeCompanyId);
            });

        $result = $this->tableGateway->selectWith($select);

        return $result->count() !== 0;
    }
}
