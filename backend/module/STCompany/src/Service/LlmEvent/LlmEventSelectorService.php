<?php

namespace STCompany\Service\LlmEvent;

use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompanyLlmEventsTable;
use STCompany\Entity\LlmEvent\LlmEvent;
use STCompany\Entity\LlmEvent\LlmEventCollection;
use STLib\Expand\Collection;

class LlmEventSelectorService
{
    public function __construct(private readonly CompanyLlmEventsTable $llmEventsTable)
    {
    }

    /**
     * @param int $companyId
     * @return LlmEventCollection
     */
    public function getLlmEvents(int $companyId): Collection
    {
        return $this->llmEventsTable->getLlmEvents($companyId);
    }

    /**
     * @throws NotFoundApiException
     */
    public function getLlmEvent(int $id, int $companyId): LlmEvent
    {
        return $this->llmEventsTable->getLlmEvent($id, $companyId);
    }
}
