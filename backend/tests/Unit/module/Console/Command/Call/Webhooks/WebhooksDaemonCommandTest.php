<?php

declare(strict_types=1);

namespace tests\Unit\module\Console\Command\Call\Webhooks;

use Console\Command\Call\Export\CallExportDaemonCommand;
use Console\Command\Call\Webhooks\WebhooksDaemonCommand;
use Laminas\Cli\Input\ParamAwareInputInterface;
use PHPUnit\Framework\MockObject\Exception;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use STCall\Daemon\Webhooks\WebhooksDaemon;
use STRabbit\Service\DaemonService;
use Symfony\Component\Console\Exception\ExceptionInterface;
use Symfony\Component\Console\Output\OutputInterface;
use tests\TestCase;

final class WebhooksDaemonCommandTest extends TestCase
{
    /**
     * @return void
     * @throws Exception
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws ExceptionInterface
     */
    public function testRun(): void
    {
        $daemon = $this->createMock(WebhooksDaemon::class);
        $daemon
            ->expects(self::once())
            ->method('setQueueName')
            ->with(WebhooksDaemon::WEBHOOKS_QUEUE_NAME)
            ->willReturnSelf();
        $daemon
            ->expects(self::once())
            ->method('setErrorQueueName')
            ->with(WebhooksDaemon::WEBHOOKS_QUEUE_ERROR_NAME);
        $this->serviceManager->setService(WebhooksDaemon::class, $daemon);

        $daemonService = $this->createMock(DaemonService::class);
        $daemonService->expects($this->once())->method('run')->with($daemon);
        $this->serviceManager->setService(DaemonService::class, $daemonService);

        $input = $this->createMock(ParamAwareInputInterface::class);
        $output = $this->createMock(OutputInterface::class);

        /**
         * @var WebhooksDaemonCommand $command
         */
        $command = $this->serviceManager->get(WebhooksDaemonCommand::class);
        $result = $command->run($input, $output);

        $this->assertSame(1, $result);
    }
}
